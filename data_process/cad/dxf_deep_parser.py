import ezdxf
import json
import re
from collections import defaultdict
from typing import Dict, List, Any, Optional


def extract_all_text_from_dxf(dxf_path: str) -> Dict[str, Any]:
    """
    深度提取DXF文件中的所有文本内容，包括块定义、图层信息等
    """
    try:
        doc = ezdxf.readfile(dxf_path)
    except Exception as e:
        return {"错误": f"无法读取DXF文件: {e}"}
    
    result = {
        "文件信息": {
            "文件路径": dxf_path,
            "DXF版本": doc.dxfversion
        },
        "模型空间文本": [],
        "图纸空间文本": [],
        "块定义文本": {},
        "图层信息": {},
        "表格文本": [],
        "所有文本汇总": [],
        "统计信息": defaultdict(int)
    }
    
    def clean_text(text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        # 移除MTEXT格式化代码
        text = re.sub(r'\\[A-Za-z][0-9]*;?', '', text)
        text = re.sub(r'\\[{}]', '', text)
        text = re.sub(r'\\P', '\n', text)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()
    
    def extract_entity_text(entity, space_name: str):
        """从实体中提取文本"""
        entity_type = entity.dxftype()
        result["统计信息"][entity_type] += 1
        
        text_content = ""
        entity_info = {
            "实体类型": entity_type,
            "图层": entity.dxf.layer,
            "空间": space_name,
            "文本内容": "",
            "坐标": [],
            "其他属性": {}
        }
        
        try:
            if entity_type == 'TEXT':
                text_content = clean_text(entity.dxf.text)
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "高度": entity.dxf.height,
                    "旋转": entity.dxf.rotation
                }
                
            elif entity_type == 'MTEXT':
                text_content = clean_text(entity.plain_text())
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "字符高度": entity.dxf.char_height,
                    "旋转": entity.dxf.rotation,
                    "宽度": getattr(entity.dxf, 'width', 0)
                }
                
            elif entity_type == 'ATTRIB':
                text_content = clean_text(entity.dxf.text)
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "标签": entity.dxf.tag,
                    "高度": entity.dxf.height
                }
                
            elif entity_type == 'ATTDEF':
                text_content = clean_text(entity.dxf.text)
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "标签": entity.dxf.tag,
                    "提示": getattr(entity.dxf, 'prompt', ''),
                    "默认值": getattr(entity.dxf, 'text', '')
                }
                
            elif entity_type == 'INSERT':
                # 处理块插入的属性
                attrib_texts = []
                if hasattr(entity, 'attribs'):
                    for attrib in entity.attribs:
                        if hasattr(attrib.dxf, 'text') and attrib.dxf.text:
                            clean_attrib = clean_text(attrib.dxf.text)
                            if clean_attrib:
                                attrib_texts.append(f"{attrib.dxf.tag}: {clean_attrib}")
                
                text_content = "; ".join(attrib_texts)
                entity_info["坐标"] = list(entity.dxf.insert)
                entity_info["其他属性"] = {
                    "块名": entity.dxf.name,
                    "缩放": [entity.dxf.xscale, entity.dxf.yscale, entity.dxf.zscale],
                    "旋转": entity.dxf.rotation,
                    "属性数量": len(attrib_texts)
                }
                
            elif entity_type in ['DIMENSION', 'LEADER']:
                dim_text = getattr(entity.dxf, 'text', '')
                if dim_text:
                    text_content = clean_text(dim_text)
                    
        except Exception as e:
            entity_info["错误"] = f"提取文本时出错: {e}"
        
        entity_info["文本内容"] = text_content
        
        if text_content:
            result["所有文本汇总"].append(text_content)
            
            if space_name == "模型空间":
                result["模型空间文本"].append(entity_info)
            elif space_name == "图纸空间":
                result["图纸空间文本"].append(entity_info)
            else:
                if space_name not in result["块定义文本"]:
                    result["块定义文本"][space_name] = []
                result["块定义文本"][space_name].append(entity_info)
    
    # 1. 提取模型空间的文本
    print("正在提取模型空间文本...")
    msp = doc.modelspace()
    for entity in msp:
        extract_entity_text(entity, "模型空间")
    
    # 2. 提取图纸空间的文本
    print("正在提取图纸空间文本...")
    for layout in doc.layouts:
        if layout.name != 'Model':  # 跳过模型空间
            for entity in layout:
                extract_entity_text(entity, f"图纸空间-{layout.name}")
    
    # 3. 提取块定义中的文本
    print("正在提取块定义文本...")
    for block in doc.blocks:
        block_name = block.name
        if not block_name.startswith('*'):  # 跳过匿名块
            for entity in block:
                extract_entity_text(entity, f"块定义-{block_name}")
    
    # 4. 提取图层信息
    print("正在提取图层信息...")
    for layer in doc.layers:
        layer_name = layer.dxf.name
        result["图层信息"][layer_name] = {
            "颜色": layer.dxf.color,
            "线型": layer.dxf.linetype,
            "是否冻结": layer.is_frozen(),
            "是否锁定": layer.is_locked(),
            "是否关闭": layer.is_off()
        }
    
    # 5. 尝试提取表格实体（如果存在）
    print("正在查找表格实体...")
    try:
        for entity in msp.query('TABLE'):
            result["表格文本"].append({
                "表格信息": "找到TABLE实体",
                "图层": entity.dxf.layer
            })
    except:
        pass
    
    # 6. 搜索特定关键词
    search_keywords = ["消防设备应急电源", "消防", "应急", "电源", "设备"]
    keyword_results = {}
    
    for keyword in search_keywords:
        matches = []
        for text in result["所有文本汇总"]:
            if keyword in text:
                matches.append(text)
        keyword_results[keyword] = matches
    
    result["关键词搜索"] = keyword_results
    
    return result


def save_deep_analysis(dxf_path: str, output_path: str):
    """
    执行深度分析并保存结果
    """
    print(f"开始深度分析DXF文件: {dxf_path}")
    
    analysis_result = extract_all_text_from_dxf(dxf_path)
    
    # 保存完整结果
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, ensure_ascii=False, indent=2)
    
    # 打印统计信息
    print(f"\n=== 深度分析完成 ===")
    print(f"实体统计:")
    for entity_type, count in sorted(analysis_result["统计信息"].items()):
        print(f"  {entity_type}: {count}")
    
    print(f"\n文本统计:")
    print(f"  模型空间文本: {len(analysis_result['模型空间文本'])}")
    print(f"  图纸空间文本: {len(analysis_result['图纸空间文本'])}")
    print(f"  块定义文本: {sum(len(texts) for texts in analysis_result['块定义文本'].values())}")
    print(f"  总文本数量: {len(analysis_result['所有文本汇总'])}")
    print(f"  图层数量: {len(analysis_result['图层信息'])}")

    print(f"\n关键词搜索结果:")
    for keyword, matches in analysis_result["关键词搜索"].items():
        print(f"  '{keyword}': {len(matches)} 条匹配")
        if matches:
            for i, match in enumerate(matches[:3]):  # 显示前3条
                print(f"    {i+1}. {match}")
            if len(matches) > 3:
                print(f"    ... 还有 {len(matches) - 3} 条")

    print(f"\n结果已保存到: {output_path}")
    
    # 创建简化的文本摘要文件
    summary_path = output_path.replace('.json', '_summary.txt')
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("=== DXF文件文本内容摘要 ===\n\n")
        f.write(f"文件: {dxf_path}\n")
        f.write(f"总文本数量: {len(analysis_result['所有文本汇总'])}\n\n")

        f.write("=== 所有文本内容 ===\n")
        for i, text in enumerate(analysis_result['所有文本汇总'], 1):
            f.write(f"{i}. {text}\n")

        f.write(f"\n=== 关键词搜索结果 ===\n")
        for keyword, matches in analysis_result["关键词搜索"].items():
            f.write(f"\n'{keyword}' ({len(matches)} 条):\n")
            for match in matches:
                f.write(f"  - {match}\n")
    
    print(f"文本摘要已保存到: {summary_path}")


if __name__ == '__main__':
    # dxf_file = '/Users/<USER>/Downloads/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图.dxf'
    dxf_file = '/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/中广核/图纸格式转化/BJ0EE296301DETX43DD11ACFC0BEE屋顶层火灾自动报警系统平面布置图.dxf'
    output_file = 'output_deep_analysis_BJ0EE296301DETX43DD11ACFC0BEE屋顶层火灾自动报警系统平面布置图.json'
    save_deep_analysis(dxf_file, output_file)
