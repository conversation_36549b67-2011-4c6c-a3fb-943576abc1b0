import json
from typing import Dict, List, Any


def search_in_deep_analysis(search_term: str):
    """在深度分析结果中搜索内容的具体位置"""
    
    try:
        with open('output_deep_analysis.json', 'r', encoding='utf-8') as f:
            deep_data = json.load(f)
    except Exception as e:
        print(f"错误: 无法读取深度分析文件: {e}")
        return
    
    print(f"🔍 在深度分析结果中搜索 '{search_term}':")
    print("-" * 60)
    
    found_locations = []
    
    # 搜索模型空间文本
    for i, text_entity in enumerate(deep_data.get("模型空间文本", [])):
        content = text_entity.get("文本内容", "")
        if search_term in content:
            found_locations.append({
                "位置": "模型空间",
                "索引": i,
                "实体类型": text_entity.get("实体类型", ""),
                "图层": text_entity.get("图层", ""),
                "内容": content,
                "坐标": text_entity.get("坐标", [])
            })
    
    # 搜索块定义文本
    for block_name, block_texts in deep_data.get("块定义文本", {}).items():
        for i, text_entity in enumerate(block_texts):
            content = text_entity.get("文本内容", "")
            if search_term in content:
                found_locations.append({
                    "位置": f"块定义-{block_name}",
                    "索引": i,
                    "实体类型": text_entity.get("实体类型", ""),
                    "图层": text_entity.get("图层", ""),
                    "内容": content,
                    "坐标": text_entity.get("坐标", [])
                })
    
    # 搜索详细文本实体
    for i, text_entity in enumerate(deep_data.get("详细文本实体", [])):
        content = text_entity.get("文本内容", "")
        if search_term in content:
            found_locations.append({
                "位置": text_entity.get("空间", "未知空间"),
                "索引": i,
                "实体类型": text_entity.get("实体类型", ""),
                "图层": text_entity.get("图层", ""),
                "内容": content,
                "坐标": text_entity.get("坐标", [])
            })
    
    # 打印结果
    if found_locations:
        print(f"✅ 找到 {len(found_locations)} 处包含 '{search_term}' 的内容:")
        
        for i, location in enumerate(found_locations, 1):
            print(f"\n{i}. 📍 位置: {location['位置']}")
            print(f"   📄 实体类型: {location['实体类型']}")
            print(f"   🎨 图层: {location['图层']}")
            print(f"   📝 内容: {location['内容']}")
            if location['坐标']:
                print(f"   📐 坐标: ({location['坐标'][0]:.1f}, {location['坐标'][1]:.1f})")
    else:
        print(f"❌ 未找到包含 '{search_term}' 的内容")


def analyze_table_structure():
    """分析表格结构，找出包含目标内容的表格"""
    
    try:
        with open('output_structured_v2.json', 'r', encoding='utf-8') as f:
            structured_data = json.load(f)
    except Exception as e:
        print(f"错误: 无法读取结构化文件: {e}")
        return
    
    print(f"\n📊 分析表格结构:")
    print("-" * 60)
    
    target_terms = ["消防设备应急电源", "应急照明控制器", "设备名称"]
    
    for sheet_idx, sheet in enumerate(structured_data.get("图稿列表", [])):
        sheet_name = sheet.get("图稿名称", f"图稿-{sheet_idx+1}")
        tables = sheet.get("表格数据", [])
        
        print(f"\n📄 {sheet_name} (共 {len(tables)} 个表格):")
        
        for table_idx, table in enumerate(tables):
            table_type = table.get("表格类型", "未知")
            rows = table.get("行数", 0)
            cols = table.get("列数", 0)
            
            # 检查表格中是否包含目标内容
            found_terms = []
            
            # 检查表头
            for header in table.get("表头", []):
                for term in target_terms:
                    if term in str(header):
                        found_terms.append(f"表头: {header}")
            
            # 检查数据行
            for row_idx, row in enumerate(table.get("数据行", [])):
                for cell in row:
                    for term in target_terms:
                        if term in str(cell):
                            found_terms.append(f"数据行{row_idx+1}: {cell}")
            
            # 检查键值对
            for key, value in table.get("键值对", {}).items():
                for term in target_terms:
                    if term in str(key) or term in str(value):
                        found_terms.append(f"键值对: {key} -> {value}")
            
            if found_terms:
                print(f"  📊 表格{table_idx+1} ({table_type}, {rows}行×{cols}列) - 包含目标内容:")
                for term in found_terms:
                    print(f"    ✓ {term}")
            else:
                print(f"  📊 表格{table_idx+1} ({table_type}, {rows}行×{cols}列) - 无目标内容")


def create_content_map():
    """创建内容地图，显示各种内容在图稿中的分布"""
    
    try:
        with open('output_structured_v2.json', 'r', encoding='utf-8') as f:
            structured_data = json.load(f)
    except Exception as e:
        print(f"错误: 无法读取结构化文件: {e}")
        return
    
    print(f"\n🗺️  内容分布地图:")
    print("=" * 80)
    
    for sheet_idx, sheet in enumerate(structured_data.get("图稿列表", [])):
        sheet_name = sheet.get("图稿名称", f"图稿-{sheet_idx+1}")
        stats = sheet.get("统计信息", {})
        
        print(f"\n📄 {sheet_name}:")
        print(f"   📊 统计: 标题栏({stats.get('标题栏文本数', 0)}) | "
              f"图例({stats.get('图例文本数', 0)}) | "
              f"表格({stats.get('表格文本数', 0)}) | "
              f"内容({stats.get('内容文本数', 0)})")
        
        # 标题栏重要信息
        title_block = sheet.get("标题栏", {})
        important_title_info = []
        for key, value in title_block.items():
            if key in ["图纸名称", "项目名称", "设计日期", "比例"] and value:
                important_title_info.append(f"{key}: {value}")
        
        if important_title_info:
            print(f"   📝 标题栏: {' | '.join(important_title_info)}")
        
        # 表格类型分布
        tables = sheet.get("表格数据", [])
        table_types = {}
        for table in tables:
            table_type = table.get("表格类型", "未知")
            table_types[table_type] = table_types.get(table_type, 0) + 1
        
        if table_types:
            type_str = " | ".join([f"{t}({c}个)" for t, c in table_types.items()])
            print(f"   📊 表格类型: {type_str}")
        
        # 图纸内容分类
        content = sheet.get("图纸内容", {})
        content_summary = []
        for content_type, items in content.items():
            if isinstance(items, list) and items:
                content_summary.append(f"{content_type}({len(items)})")
            elif isinstance(items, dict):
                total_items = sum(len(v) if isinstance(v, list) else 1 for v in items.values())
                if total_items > 0:
                    content_summary.append(f"{content_type}({total_items})")
        
        if content_summary:
            print(f"   📐 内容分类: {' | '.join(content_summary)}")


def main():
    """主函数"""
    print("🔍 DXF 内容详细搜索和分析")
    print("=" * 80)
    
    # 搜索特定内容
    search_terms = ["消防设备应急电源", "应急照明控制器", "设备名称"]
    
    for term in search_terms:
        search_in_deep_analysis(term)
        print()
    
    # 分析表格结构
    analyze_table_structure()
    
    # 创建内容地图
    create_content_map()


if __name__ == '__main__':
    main()
