import json
from typing import Dict, Any


def print_structured_summary(json_file: str):
    """打印结构化解析结果的摘要"""
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"错误: 无法读取文件 {json_file}: {e}")
        return
    
    print("=" * 80)
    print("DXF 文件结构化解析结果摘要")
    print("=" * 80)
    
    # 文件信息
    file_info = data.get("文件信息", {})
    print(f"\n📁 文件信息:")
    print(f"   文件路径: {file_info.get('文件路径', 'N/A')}")
    print(f"   DXF版本: {file_info.get('DXF版本', 'N/A')}")
    print(f"   图稿数量: {file_info.get('图稿数量', 0)}")
    print(f"   总文本数: {file_info.get('总文本实体数', 0)}")
    
    # 图稿列表
    sheets = data.get("图稿列表", [])
    print(f"\n📋 图稿详情 (共 {len(sheets)} 个图稿):")
    
    for i, sheet in enumerate(sheets):
        print(f"\n  📄 {sheet.get('图稿名称', f'图稿-{i+1}')}:")
        
        # 图框信息
        frame_info = sheet.get("图框信息", {})
        size_info = frame_info.get("尺寸", {})
        print(f"     🔲 图框: {frame_info.get('类型', 'N/A')} "
              f"({size_info.get('宽度', 0):.0f} × {size_info.get('高度', 0):.0f})")
        
        # 统计信息
        stats = sheet.get("统计信息", {})
        print(f"     📊 内容统计:")
        print(f"        标题栏文本: {stats.get('标题栏文本数', 0)} 条")
        print(f"        图例文本: {stats.get('图例文本数', 0)} 条")
        print(f"        表格文本: {stats.get('表格文本数', 0)} 条")
        print(f"        内容文本: {stats.get('内容文本数', 0)} 条")
        print(f"        表格数量: {len(sheet.get('表格数据', []))} 个")
        
        # 标题栏信息
        title_block = sheet.get("标题栏", {})
        print(f"     📝 标题栏信息:")
        if title_block.get("设计日期"):
            print(f"        设计日期: {title_block['设计日期']}")
        if title_block.get("比例"):
            print(f"        比例: {title_block['比例']}")
        
        # 表格数据摘要
        tables = sheet.get("表格数据", [])
        if tables:
            print(f"     📊 表格摘要:")
            for j, table in enumerate(tables):
                table_type = table.get("表格类型", "未知")
                rows = table.get("行数", 0)
                cols = table.get("列数", 0)
                print(f"        表格{j+1}: {table_type} ({rows}行 × {cols}列)")
                
                # 显示键值对（如果有）
                key_values = table.get("键值对", {})
                if key_values:
                    print(f"          关键信息:")
                    for key, value in list(key_values.items())[:3]:  # 只显示前3个
                        print(f"            {key}: {value}")
                    if len(key_values) > 3:
                        print(f"            ... 还有 {len(key_values) - 3} 项")
    
    # 全局统计
    global_stats = data.get("全局统计", {})
    print(f"\n📈 全局统计:")
    
    # 实体类型统计
    entity_stats = global_stats.get("实体类型统计", {})
    if entity_stats:
        print(f"   实体类型分布:")
        for entity_type, count in sorted(entity_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"     {entity_type}: {count}")
    
    # 图层统计
    layer_stats = global_stats.get("图层统计", {})
    if layer_stats:
        print(f"\n   主要图层 (前10个):")
        sorted_layers = sorted(layer_stats.items(), key=lambda x: x[1], reverse=True)[:10]
        for layer, count in sorted_layers:
            print(f"     {layer}: {count}")
    
    # 关键词搜索结果
    keyword_search = global_stats.get("关键词搜索", {})
    if keyword_search:
        print(f"\n🔍 关键词搜索结果:")
        for keyword, result in keyword_search.items():
            count = result.get("匹配数量", 0)
            print(f"   '{keyword}': {count} 条匹配")
            
            # 显示部分匹配内容
            matches = result.get("匹配内容", [])
            if matches:
                for i, match in enumerate(matches[:2]):  # 只显示前2条
                    print(f"     {i+1}. {match}")
                if len(matches) > 2:
                    print(f"     ... 还有 {len(matches) - 2} 条")
    
    print("\n" + "=" * 80)


def find_specific_content(json_file: str, search_term: str):
    """查找特定内容在哪个图稿的哪个区域"""
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"错误: 无法读取文件 {json_file}: {e}")
        return
    
    print(f"\n🔍 搜索 '{search_term}' 的详细位置:")
    print("-" * 60)
    
    found_count = 0
    
    # 在每个图稿中搜索
    for sheet in data.get("图稿列表", []):
        sheet_name = sheet.get("图稿名称", "未知图稿")
        
        # 搜索标题栏
        title_block = sheet.get("标题栏", {})
        for key, value in title_block.items():
            if isinstance(value, str) and search_term in value:
                print(f"📄 {sheet_name} - 标题栏 - {key}: {value}")
                found_count += 1
            elif isinstance(value, list):
                for item in value:
                    if search_term in str(item):
                        print(f"📄 {sheet_name} - 标题栏 - {key}: {item}")
                        found_count += 1
        
        # 搜索表格数据
        for i, table in enumerate(sheet.get("表格数据", [])):
            table_type = table.get("表格类型", "未知")
            
            # 搜索键值对
            for key, value in table.get("键值对", {}).items():
                if search_term in key or search_term in value:
                    print(f"📊 {sheet_name} - 表格{i+1}({table_type}) - {key}: {value}")
                    found_count += 1
            
            # 搜索表头和数据行
            for header in table.get("表头", []):
                if search_term in header:
                    print(f"📊 {sheet_name} - 表格{i+1}({table_type}) - 表头: {header}")
                    found_count += 1
            
            for row in table.get("数据行", []):
                for cell in row:
                    if search_term in str(cell):
                        print(f"📊 {sheet_name} - 表格{i+1}({table_type}) - 数据: {cell}")
                        found_count += 1
        
        # 搜索图纸内容
        content = sheet.get("图纸内容", {})
        for content_type, items in content.items():
            if isinstance(items, list):
                for item in items:
                    if search_term in str(item):
                        print(f"📐 {sheet_name} - 图纸内容 - {content_type}: {item}")
                        found_count += 1
            elif isinstance(items, dict):
                for key, value in items.items():
                    if isinstance(value, list):
                        for item in value:
                            if search_term in str(item):
                                print(f"📐 {sheet_name} - 图纸内容 - {content_type}({key}): {item}")
                                found_count += 1
    
    if found_count == 0:
        print(f"❌ 未找到包含 '{search_term}' 的内容")
    else:
        print(f"\n✅ 总共找到 {found_count} 处包含 '{search_term}' 的内容")


def main():
    """主函数"""
    json_file = 'output_structured_v2.json'
    
    # 打印结构化摘要
    print_structured_summary(json_file)
    
    # 搜索特定内容
    search_terms = ["消防设备应急电源", "应急照明控制器"]
    
    for term in search_terms:
        find_specific_content(json_file, term)


if __name__ == '__main__':
    main()
