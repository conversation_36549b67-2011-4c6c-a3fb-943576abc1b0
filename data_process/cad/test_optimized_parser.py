#!/usr/bin/env python3
"""
测试优化后的DXF深度解析器
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from dxf_deep_parser import process_dxf_batch, get_dxf_files, create_output_structure


def test_functions():
    """测试各个函数的功能"""
    
    print("=== 测试DXF深度解析器优化版本 ===\n")
    
    # 测试路径（请根据实际情况修改）
    test_file_path = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/中广核/图纸格式转化/BJ0EE296301DETX43DD11ACFC0BEE屋顶层火灾自动报警系统平面布置图.dxf"
    test_dir_path = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/中广核/图纸格式转化"
    
    # 1. 测试获取DXF文件列表功能
    print("1. 测试获取DXF文件列表功能")
    
    if os.path.exists(test_file_path):
        print(f"测试单个文件: {test_file_path}")
        files = get_dxf_files(test_file_path)
        print(f"找到文件数量: {len(files)}")
        for f in files:
            print(f"  - {f}")
    else:
        print(f"测试文件不存在: {test_file_path}")
    
    print()
    
    if os.path.exists(test_dir_path):
        print(f"测试目录: {test_dir_path}")
        files = get_dxf_files(test_dir_path)
        print(f"找到文件数量: {len(files)}")
        for f in files[:5]:  # 只显示前5个
            print(f"  - {f.name}")
        if len(files) > 5:
            print(f"  ... 还有 {len(files) - 5} 个文件")
    else:
        print(f"测试目录不存在: {test_dir_path}")
    
    print("\n" + "="*50)
    
    # 2. 测试输出目录创建功能
    print("2. 测试输出目录创建功能")
    
    if os.path.exists(test_file_path):
        output_dir = create_output_structure(test_file_path)
        print(f"单文件输出目录: {output_dir}")
    
    if os.path.exists(test_dir_path):
        output_dir = create_output_structure(test_dir_path)
        print(f"目录输出目录: {output_dir}")
    
    print("\n" + "="*50)
    
    # 3. 测试批量处理功能（仅显示计划，不实际执行）
    print("3. 批量处理功能测试计划")
    
    if os.path.exists(test_dir_path):
        print(f"如果执行批量处理，将处理目录: {test_dir_path}")
        files = get_dxf_files(test_dir_path)
        output_dir = create_output_structure(test_dir_path)
        
        print(f"将处理 {len(files)} 个DXF文件")
        print(f"输出目录: {output_dir}")
        print("输出文件列表:")
        
        for dxf_file in files[:5]:  # 只显示前5个
            output_filename = f"{dxf_file.stem}.json"
            print(f"  {dxf_file.name} -> {output_filename}")
        
        if len(files) > 5:
            print(f"  ... 还有 {len(files) - 5} 个文件")
    
    print("\n" + "="*50)
    print("测试完成！")


def run_actual_processing():
    """
    实际运行处理（谨慎使用）
    """
    print("=== 实际运行批量处理 ===")
    
    # 请根据需要修改这个路径
    input_path = "/Users/<USER>/work/移动/项目-农商文旅/00-00 大数据/中广核/图纸格式转化"
    
    if not os.path.exists(input_path):
        print(f"输入路径不存在: {input_path}")
        return
    
    try:
        results = process_dxf_batch(input_path)
        print(f"\n处理结果: {results}")
    except Exception as e:
        print(f"处理过程中出错: {e}")


if __name__ == "__main__":
    # 默认只运行测试，不实际处理
    test_functions()
    
    # 如果要实际运行处理，请取消下面的注释
    # print("\n" + "="*60)
    # run_actual_processing()
