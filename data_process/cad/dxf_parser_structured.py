import ezdxf
import json
from collections import defaultdict
from ezdxf import bbox as ezdxf_bbox  # 使用新的bbox模块


def get_entity_bbox(entity):
    """
    使用新的ezdxf.bbox模块计算实体的包围盒，更健壮和准确。
    """
    try:
        # ezdxf.bbox.extents可以处理绝大多数实体类型
        return ezdxf_bbox.extents([entity])
    except (RuntimeError, TypeError):
        # 对于某些不支持的实体或特殊情况，返回None
        return None


def find_drawing_frames(msp, min_size_ratio=0.1):
    """
    在模型空间中找到主要的图纸边框。
    """
    frames = []
    try:
        model_bbox = ezdxf_bbox.extents(msp)
        if not model_bbox.has_data:
            return []
        model_diag = model_bbox.size
        min_width = model_diag.x * min_size_ratio
        min_height = model_diag.y * min_size_ratio
    except (IndexError, ezdxf.DXFValueError):
        return []

    # 【核心修复】先查询实体类型，再在Python中过滤属性
    lwpolylines = msp.query('LWPOLYLINE')
    for entity in lwpolylines:
        if entity.is_closed and len(entity) == 4:  # 检查是否闭合且为矩形
            bbox = get_entity_bbox(entity)
            if bbox and bbox.size.x > min_width and bbox.size.y > min_height:
                frames.append(entity)

    # 过滤掉被其他框包含的小框，确保找到的是最外层的图框
    final_frames = []
    for f1 in frames:
        is_outermost = True
        bbox1 = get_entity_bbox(f1)
        if not bbox1: continue
        for f2 in frames:
            if f1 is f2: continue
            bbox2 = get_entity_bbox(f2)
            if bbox2 and bbox2.contains(bbox1):
                is_outermost = False
                break
        if is_outermost:
            final_frames.append(f1)

    return final_frames


def parse_table_content(table_entities):
    """
    一个简化的表格内容解析器。使用动态公差进行文本行分组。
    """
    texts = [e for e in table_entities if e.dxftype() in {'TEXT', 'MTEXT'}]
    if not texts:
        return {}

    # 计算平均文本高度作为行分组的容差依据
    valid_texts = [t for t in texts if hasattr(t.dxf, 'height') and t.dxf.height > 0]
    avg_text_height = sum(t.dxf.height for t in valid_texts) / len(valid_texts) if valid_texts else 1.0
    tolerance = avg_text_height * 0.5

    rows = defaultdict(list)
    sorted_texts = sorted(texts, key=lambda t: t.dxf.insert.y, reverse=True)

    if not sorted_texts: return {}

    # 按Y坐标（行）对文本进行分组
    current_row_y = sorted_texts[0].dxf.insert.y
    rows[current_row_y].append(sorted_texts[0])

    for i in range(1, len(sorted_texts)):
        text = sorted_texts[i]
        if abs(text.dxf.insert.y - current_row_y) <= tolerance:
            rows[current_row_y].append(text)
        else:
            current_row_y = text.dxf.insert.y
            rows[current_row_y].append(text)

    parsed_items = {}
    for y, row_texts in rows.items():
        sorted_row = sorted(row_texts, key=lambda t: t.dxf.insert.x)
        # 【逻辑优化】严格按2个一组进行键值配对
        for i in range(0, len(sorted_row) - 1, 2):
            key_text = sorted_row[i].dxf.text.strip().replace(":", "").replace("：", "")
            value_text = sorted_row[i + 1].dxf.text.strip()
            if key_text and value_text:
                parsed_items[key_text] = value_text

    return parsed_items


def advanced_dxf_parser(dxf_path):
    """
    高级DXF解析器，将图纸内容结构化。
    """
    try:
        doc = ezdxf.readfile(dxf_path)
    except Exception as e:
        return {"错误": f"无法读取或解析文件: {e}"}

    msp = doc.modelspace()
    all_entities = list(msp)
    frames = find_drawing_frames(msp)

    if not frames:
        return {"警告": "未找到有效的主图框，无法进行结构化解析。"}

    output = {
        "文件元数据": {
            "文件名": dxf_path,
            "DXF版本": doc.dxfversion,
        },
        "图稿列表": []
    }

    for i, frame in enumerate(frames):
        frame_bbox = get_entity_bbox(frame)
        if not frame_bbox: continue

        drawing_sheet = {
            "图稿索引": i,
            "图框包围盒": {
                "min_point": list(frame_bbox.extmin),
                "max_point": list(frame_bbox.extmax)
            },
            "图纸内容": [],
            "表格信息": {}
        }

        # 启发式规则：定义表格可能存在的区域（图框右下角）
        table_bbox = ezdxf_bbox.BoundingBox(
            (frame_bbox.extmax.x - frame_bbox.size.x * 0.5, frame_bbox.extmin.y),
            (frame_bbox.extmax.x, frame_bbox.extmin.y + frame_bbox.size.y * 0.4)
        )

        table_candidate_entities = []

        for entity in all_entities:
            if entity is frame: continue
            entity_bbox = get_entity_bbox(entity)
            if entity_bbox and frame_bbox.contains(entity_bbox):
                is_table_entity = False
                # 优先按图层名称判断是否属于表格
                if entity.dxf.layer.upper() in ["TABLE", "TITLE", "BORDERS", "标题栏", "图框"]:
                    is_table_entity = True
                # 其次按几何位置判断
                elif table_bbox.contains(entity_bbox):
                    is_table_entity = True

                entity_info = {"实体类型": entity.dxftype(), "图层": entity.dxf.layer, "颜色": entity.dxf.color}

                if is_table_entity:
                    table_candidate_entities.append(entity)
                else:
                    drawing_sheet["图纸内容"].append(entity_info)

        drawing_sheet["表格信息"] = parse_table_content(table_candidate_entities)
        output["图稿列表"].append(drawing_sheet)

    return output


if __name__ == '__main__':
    # 请将 'your_drawing.dxf' 替换为您的DXF文件实际路径
    dxf_file = '/Users/<USER>/Downloads/BJ0EEX96101DETX43DD11CCFC0BEE火灾自动报警系统配线图.dxf'
    output_json_path = 'output_structured.json'

    try:
        structured_data = advanced_dxf_parser(dxf_file)
        with open(output_json_path, 'w', encoding='utf-8') as f:
            json.dump(structured_data, f, ensure_ascii=False, indent=4)
        print(f"✅ 结构化解析完成，结果已保存至 {output_json_path}")
    except FileNotFoundError:
        print(f"❌ 错误：输入文件未找到 -> {dxf_file}")
    except Exception as e:
        print(f"❌ 发生未知错误: {e}")